package com.kerryprops.kip.riskcontrol.login;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for login scenario risk assessment.
 */
@Slf4j
@RestController
@RequestMapping("/api/risk-assessment/login")
@RequiredArgsConstructor
@ConditionalOnProperty(name = "drools.enabled", havingValue = "true", matchIfMissing = true)
@Tag(name = "Login Risk Assessment", description = "Login scenario risk assessment endpoints")
public class LoginRiskAssessmentController {

    private final LoginRiskAssessmentService loginRiskAssessmentService;

    @Operation(
            summary = "Assess login risk",
            description = "Assess risk for login scenario including IP location, phone number validation, " +
                    "member points validation, ID validation limits and member status checks"
    )
    @PostMapping
    public LoginRiskAssessResponse assessLoginRisk(@Valid @RequestBody LoginRiskAssessRequest request) {
        log.info("Received login risk assessment request - mall: {}, IP: {}",
                request.getMallCode(), request.getIpAddress());

        LoginRiskAssessResponse response = loginRiskAssessmentService.assessLoginRisk(request);

        log.info("Completed login risk assessment - mall: {}, result: {}",
                request.getMallCode(), response.getRiskResult());

        return response;
    }
}
