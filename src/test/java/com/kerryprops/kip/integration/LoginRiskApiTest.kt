package com.kerryprops.kip.integration

import com.kerryprops.kip.crypto.RsaCryptoController
import com.kerryprops.kip.crypto.RsaCryptoController.PublicKeyResponse
import com.kerryprops.kip.crypto.RsaCryptoService
import io.restassured.RestAssured
import jakarta.annotation.Resource
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus

/**
 * RsaCryptoControllerIntegrationTest.
 *
 * <AUTHOR> Yu
 */
internal class LoginRiskApiTest : BaseIntegrationTest() {
    @Resource
    private val rsaService: RsaCryptoService? = null

    @Test
    @DisplayName("getRsaPublicKey-正常请求-返回公钥")
    fun getRsaPublicKey_validRequest_returnPublicKeySuccessfully() {
        // Act & Assert
        val response = RestAssured.given().`when`()
            .get("/crypto/rsa/public_key")
            .then()
            .statusCode(HttpStatus.OK.value())
            .extract()
            .`as`<PublicKeyResponse?>(PublicKeyResponse::class.java)

        Assertions.assertThat<PublicKeyResponse?>(response).isNotNull()
        Assertions.assertThat(response!!.base64PublicKey).isEqualTo(rsaService!!.getRsaPublicKey())
    }

    @Test
    @DisplayName("encryptRsa-正常请求-返回加密数据")
    fun encryptRsa_validRequest_returnEncryptedDataSuccessfully() {
        val request = RsaCryptoController.EncryptRequest("testPlainText")

        val encryptResponse = RestAssured.given().body(request)
            .`when`()
            .post("/crypto/rsa/encrypt")
            .then()
            .statusCode(HttpStatus.OK.value())
            .extract()
            .`as`<RsaCryptoController.EncryptResponse>(RsaCryptoController.EncryptResponse::class.java)

        //assert encryptedData is not null and not empty
        Assertions.assertThat(encryptResponse.encryptedData).isNotNull()
        Assertions.assertThat(encryptResponse.encryptedData).isNotEmpty()

        val decryptResponse =
            RestAssured.given().body(RsaCryptoController.DecryptRequest(encryptResponse.encryptedData))
                .`when`()
                .post("/crypto/rsa/decrypt")
                .then()
                .statusCode(HttpStatus.OK.value())
                .extract()
                .`as`<RsaCryptoController.DecryptResponse>(RsaCryptoController.DecryptResponse::class.java)

        Assertions.assertThat(decryptResponse.plainText).isEqualTo(request.plainText)
    }

    @Test
    @DisplayName("encryptRsa-无效请求参数-返回500")
    fun encryptRsa_invalidRequest_returnBadRequest() {
        // Arrange
        val invalidRequest = RsaCryptoController.EncryptRequest(null)

        // Act & Assert
        RestAssured.given().body(invalidRequest)
            .`when`()
            .post("/crypto/rsa/encrypt")
            .then()
            .statusCode(HttpStatus.INTERNAL_SERVER_ERROR.value())
    }

    @Test
    @DisplayName("decryptRsa-正常请求-返回解密数据")
    fun decryptRsa_validRequest_returnDecryptedDataSuccessfully() {
        // Arrange
        val encryptedData = rsaService!!.encrypt("testPlainText")
        val request = RsaCryptoController.DecryptRequest(encryptedData)

        // Act & Assert
        RestAssured.given().body(request)
            .`when`()
            .post("/crypto/rsa/decrypt")
            .then()
            .statusCode(HttpStatus.OK.value())
            .extract()
            .`as`<RsaCryptoController.DecryptResponse?>(RsaCryptoController.DecryptResponse::class.java)
    }
}